package com.superhexa.supervision.library.db.bean

import androidx.annotation.Keep
import androidx.compose.runtime.mutableStateOf
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

/**
 * 转写状态枚举
 */
enum class TranscriptionStatus {
    /** 未开始 */
    NONE,
    /** 上传中 */
    UPLOADING,
    /** 转写中 */
    TRANSCRIBING,
    /** 转写成功 */
    SUCCESS,
    /** 转写失败 */
    FAILED
}

/**
 * 总结状态枚举
 */
enum class SummaryStatus {
    /** 未开始 */
    NONE,
    /** 总结中 */
    SUMMARIZING,
    /** 总结成功 */
    SUCCESS,
    /** 总结失败 */
    FAILED
}

@Entity
@Keep
data class AudioTranscriptionBean(
    @Id var objId: Long = 0L,
    val id: Int,
    var path: String,
    var userId: String = "",
    var transcriptionId: String? = null,
    var summaryTaskId: String? = null,
    var srcLang: String? = null,
    var srcStr: String? = null,
    var summaryStr: String? = null,
    var isFirstShow: Boolean = true,
    var isDistinguishSpeakers: Boolean = true,
    var fileCreateTime: Long = 0L,
    var speakerName: String? = "",
    var summaryTitle: String? = "",
    var summaryTemplate: String? = null,
    var summaryErrorCode: Int = 0,
    var transcriptionStatus: String? = null,
    var summaryStatus: String? = null
) {

    @delegate:Transient
    val srtContent by lazy { mutableStateOf(srcStr) }

    @delegate:Transient
    val summaryContent by lazy { mutableStateOf(summaryStr) }

    /**
     * 获取转写状态枚举
     */
    fun getTranscriptionStatusEnum(): TranscriptionStatus {
        return try {
            if (transcriptionStatus.isNullOrEmpty()) {
                TranscriptionStatus.NONE
            } else {
                TranscriptionStatus.valueOf(transcriptionStatus!!)
            }
        } catch (e: IllegalArgumentException) {
            TranscriptionStatus.NONE
        }
    }

    /**
     * 设置转写状态
     */
    fun setTranscriptionStatus(status: TranscriptionStatus) {
        transcriptionStatus = status.name
    }

    /**
     * 获取总结状态枚举
     */
    fun getSummaryStatusEnum(): SummaryStatus {
        return try {
            if (summaryStatus.isNullOrEmpty()) {
                SummaryStatus.NONE
            } else {
                SummaryStatus.valueOf(summaryStatus!!)
            }
        } catch (e: IllegalArgumentException) {
            SummaryStatus.NONE
        }
    }

    /**
     * 设置总结状态
     */
    fun setSummaryStatus(status: SummaryStatus) {
        summaryStatus = status.name
    }
}
